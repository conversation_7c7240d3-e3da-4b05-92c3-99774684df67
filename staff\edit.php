<?php
/**
 * Apex Company Management System
 * Edit Staff Member
 */

define('APEX_ACCESS', true);
require_once '../config/config.php';
require_once '../includes/middleware.php';

// Check authentication and permission
auth_middleware();
permission_middleware('edit_users');

$staff_id = (int)($_GET['id'] ?? 0);

if (!$staff_id) {
    $_SESSION['error'] = 'Invalid staff member ID.';
    header('Location: index.php');
    exit();
}

// Get staff member details
$stmt = $mysqli->prepare("
    SELECT id, username, email, first_name, last_name, phone, role, status, salary
    FROM users
    WHERE id = ?
");
$stmt->bind_param("i", $staff_id);
$stmt->execute();
$staff = $stmt->get_result()->fetch_assoc();

if (!$staff) {
    $_SESSION['error'] = 'Staff member not found.';
    header('Location: index.php');
    exit();
}

// Check if user can edit this staff member
if ($staff_id != $_SESSION['user_id'] && $_SESSION['role'] !== 'super_admin' && 
    !($_SESSION['role'] === 'admin' && $staff['role'] === 'staff')) {
    $_SESSION['error'] = 'You do not have permission to edit this staff member.';
    header('Location: view.php?id=' . $staff_id);
    exit();
}

$error = '';
$success = '';

// Handle form submission
if ($_POST) {
    if (!verify_csrf_token($_POST['csrf_token'])) {
        $error = 'Invalid security token. Please try again.';
    } else {
        $username = trim($_POST['username'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $first_name = trim($_POST['first_name'] ?? '');
        $last_name = trim($_POST['last_name'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $role = $_POST['role'] ?? $staff['role'];
        $status = $_POST['status'] ?? $staff['status'];
        $salary = !empty($_POST['salary']) ? (float)$_POST['salary'] : null;
        $password = $_POST['password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        // Validation
        if (empty($username)) {
            $error = 'Username is required.';
        } elseif (empty($email)) {
            $error = 'Email is required.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = 'Please enter a valid email address.';
        } elseif (empty($first_name)) {
            $error = 'First name is required.';
        } elseif (empty($last_name)) {
            $error = 'Last name is required.';
        } elseif (!empty($password) && strlen($password) < PASSWORD_MIN_LENGTH) {
            $error = 'Password must be at least ' . PASSWORD_MIN_LENGTH . ' characters long.';
        } elseif (!empty($password) && $password !== $confirm_password) {
            $error = 'Passwords do not match.';
        } else {
            // Check if username already exists (excluding current user)
            $stmt = $mysqli->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $stmt->bind_param("si", $username, $staff_id);
            $stmt->execute();
            if ($stmt->get_result()->num_rows > 0) {
                $error = 'Username already exists. Please choose a different username.';
            } else {
                // Check if email already exists (excluding current user)
                $stmt = $mysqli->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->bind_param("si", $email, $staff_id);
                $stmt->execute();
                if ($stmt->get_result()->num_rows > 0) {
                    $error = 'Email already exists. Please use a different email address.';
                } else {
                    // Prepare update query
                    if (!empty($password)) {
                        $password_hash = password_hash($password, PASSWORD_DEFAULT);
                        $stmt = $mysqli->prepare("
                            UPDATE users 
                            SET username = ?, email = ?, password_hash = ?, first_name = ?, 
                                last_name = ?, phone = ?, role = ?, status = ?, updated_at = NOW()
                            WHERE id = ?
                        ");
                        $stmt->bind_param("ssssssssi", 
                            $username, $email, $password_hash, $first_name, $last_name,
                            $phone, $role, $status, $staff_id
                        );
                    } else {
                        $stmt = $mysqli->prepare("
                            UPDATE users
                            SET username = ?, email = ?, first_name = ?, last_name = ?,
                                phone = ?, role = ?, status = ?, salary = ?, updated_at = NOW()
                            WHERE id = ?
                        ");
                        $stmt->bind_param("sssssssdi",
                            $username, $email, $first_name, $last_name,
                            $phone, $role, $status, $salary, $staff_id
                        );
                    }
                    
                    if ($stmt->execute()) {
                        log_activity('Staff Member Updated', 'users', $staff_id);
                        $_SESSION['success'] = 'Staff member updated successfully!';
                        header('Location: view.php?id=' . $staff_id);
                        exit();
                    } else {
                        $error = 'Error updating staff member. Please try again.';
                    }
                }
            }
        }
    }
}

$page_title = 'Edit Staff Member - ' . $staff['first_name'] . ' ' . $staff['last_name'];
require_once '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-pencil"></i>
                    Edit Staff Member
                </h1>
                <div class="btn-group">
                    <a href="view.php?id=<?= $staff_id ?>" class="btn btn-outline-primary">
                        <i class="bi bi-eye"></i>
                        View Details
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Back to Staff
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?= htmlspecialchars($error) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <form method="POST" action="" id="staffForm">
        <?= csrf_token_input() ?>
        
        <div class="row">
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Personal Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= htmlspecialchars($_POST['first_name'] ?? $staff['first_name']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= htmlspecialchars($_POST['last_name'] ?? $staff['last_name']) ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= htmlspecialchars($_POST['email'] ?? $staff['email']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= htmlspecialchars($_POST['phone'] ?? $staff['phone']) ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Account Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?= htmlspecialchars($_POST['username'] ?? $staff['username']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required 
                                        <?= ($staff_id == $_SESSION['user_id'] && $_SESSION['role'] !== 'super_admin') ? 'disabled' : '' ?>>
                                    <?php if ($_SESSION['role'] === 'super_admin'): ?>
                                    <option value="super_admin" <?= ($_POST['role'] ?? $staff['role']) === 'super_admin' ? 'selected' : '' ?>>Super Admin</option>
                                    <option value="admin" <?= ($_POST['role'] ?? $staff['role']) === 'admin' ? 'selected' : '' ?>>Admin</option>
                                    <?php elseif ($_SESSION['role'] === 'admin' && $staff['role'] !== 'super_admin'): ?>
                                    <option value="admin" <?= ($_POST['role'] ?? $staff['role']) === 'admin' ? 'selected' : '' ?>>Admin</option>
                                    <?php endif; ?>
                                    <option value="staff" <?= ($_POST['role'] ?? $staff['role']) === 'staff' ? 'selected' : '' ?>>Staff</option>
                                </select>
                                <?php if ($staff_id == $_SESSION['user_id'] && $_SESSION['role'] !== 'super_admin'): ?>
                                <input type="hidden" name="role" value="<?= $staff['role'] ?>">
                                <div class="form-text">You cannot change your own role.</div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status"
                                        <?= ($staff_id == $_SESSION['user_id']) ? 'disabled' : '' ?>>
                                    <option value="active" <?= ($_POST['status'] ?? $staff['status']) === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= ($_POST['status'] ?? $staff['status']) === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                </select>
                                <?php if ($staff_id == $_SESSION['user_id']): ?>
                                <input type="hidden" name="status" value="<?= $staff['status'] ?>">
                                <div class="form-text">You cannot change your own status.</div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="salary" class="form-label">Monthly Salary</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="salary" name="salary"
                                           value="<?= htmlspecialchars($_POST['salary'] ?? $staff['salary'] ?? '') ?>"
                                           step="0.01" min="0" placeholder="0.00">
                                </div>
                                <div class="form-text">Leave empty if not applicable</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Change Password</h5>
                        <small class="text-muted">Leave blank to keep current password</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="password" name="password">
                                <div class="form-text">Minimum <?= PASSWORD_MIN_LENGTH ?> characters required.</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2 mb-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check"></i>
                        Update Staff Member
                    </button>
                    <a href="view.php?id=<?= $staff_id ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-x"></i>
                        Cancel
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card mb-3">
                    <div class="card-body text-center">
                        <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                            <?= strtoupper(substr($staff['first_name'], 0, 1) . substr($staff['last_name'], 0, 1)) ?>
                        </div>
                        <h5><?= htmlspecialchars($staff['first_name'] . ' ' . $staff['last_name']) ?></h5>
                        <p class="text-muted">@<?= htmlspecialchars($staff['username']) ?></p>
                        <span class="badge bg-<?= $staff['role'] === 'super_admin' ? 'danger' : ($staff['role'] === 'admin' ? 'warning' : 'info') ?>">
                            <?= ucfirst(str_replace('_', ' ', $staff['role'])) ?>
                        </span>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Important Notes</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small text-muted">
                            <li><i class="bi bi-info-circle text-primary"></i> Username must be unique across the system.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Email will be used for notifications.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Leave password fields blank to keep current password.</li>
                            <li><i class="bi bi-info-circle text-primary"></i> Role changes affect system permissions.</li>
                            <?php if ($staff_id == $_SESSION['user_id']): ?>
                            <li><i class="bi bi-exclamation-triangle text-warning"></i> You cannot change your own role or status.</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password && password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

// Clear password confirmation when password is cleared
document.getElementById('password').addEventListener('input', function() {
    const confirmPassword = document.getElementById('confirm_password');
    if (!this.value) {
        confirmPassword.value = '';
        confirmPassword.setCustomValidity('');
    }
});
</script>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    font-size: 2rem;
    font-weight: bold;
}
</style>

<?php require_once '../includes/footer.php'; ?>
